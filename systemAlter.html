<!DOCTYPE html>
<html lang="ar">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>System Alert - Critical Notification</title>
    <style>
      @import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      .pulse {
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
        100% {
          transform: scale(1);
        }
      }

      .gradient-bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      .alert-badge {
        display: inline-block;
        background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
      }

      .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .status-critical {
        background: #ff4757;
      }
      .status-warning {
        background: #ffa502;
      }
      .status-info {
        background: #3742fa;
      }
    </style>
  </head>
  <body
    style="
      margin: 0;
      padding: 0;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI',
        Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      min-height: 100vh;
    "
  >
    <!-- Main Container -->
    <table
      width="100%"
      cellpadding="0"
      cellspacing="0"
      style="padding: 30px 20px"
    >
      <tr>
        <td align="center">
          <!-- Email Card -->
          <table
            width="650"
            cellpadding="0"
            cellspacing="0"
            style="
              background: #ffffff;
              border-radius: 16px;
              overflow: hidden;
              box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
              border: 1px solid rgba(255, 255, 255, 0.2);
            "
          >
            <!-- Alert Header -->
            <tr>
              <td
                style="
                  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                  padding: 0;
                  position: relative;
                "
              >
                <table width="100%" cellpadding="0" cellspacing="0">
                  <tr>
                    <td
                      style="
                        padding: 30px;
                        color: #ffffff;
                        text-align: center;
                        position: relative;
                      "
                    >
                      <!-- Alert Icon -->
                      <div style="margin-bottom: 15px">
                        <span
                          style="font-size: 48px; display: inline-block"
                          class="pulse"
                          >⚠️</span
                        >
                      </div>

                      <!-- Title -->
                      <h1
                        style="
                          margin: 0;
                          font-size: 28px;
                          font-weight: 700;
                          letter-spacing: -0.5px;
                          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                        "
                      >
                        CRITICAL SYSTEM ALERT
                      </h1>

                      <!-- Subtitle -->
                      <p
                        style="
                          margin: 8px 0 0;
                          font-size: 16px;
                          opacity: 0.9;
                          font-weight: 400;
                        "
                      >
                        Immediate attention required
                      </p>

                      <!-- Alert Badge -->
                      <div style="margin-top: 15px">
                        <span class="alert-badge">URGENT</span>
                      </div>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>

            <!-- Alert Content -->
            <tr>
              <td style="padding: 0">
                <!-- Summary Section -->
                <table width="100%" cellpadding="0" cellspacing="0">
                  <tr>
                    <td
                      style="
                        padding: 35px 40px 25px;
                        border-bottom: 1px solid #f0f0f0;
                      "
                    >
                      <table width="100%" cellpadding="0" cellspacing="0">
                        <tr>
                          <td>
                            <h2
                              style="
                                margin: 0 0 15px;
                                font-size: 22px;
                                font-weight: 600;
                                color: #2c3e50;
                                display: flex;
                                align-items: center;
                              "
                            >
                              <span
                                style="
                                  display: inline-block;
                                  width: 4px;
                                  height: 22px;
                                  background: linear-gradient(
                                    to bottom,
                                    #3498db,
                                    #2980b9
                                  );
                                  margin-right: 12px;
                                  border-radius: 2px;
                                "
                              ></span>
                              📋 Summary
                            </h2>
                            <div
                              style="
                                background: linear-gradient(
                                  135deg,
                                  #74b9ff 0%,
                                  #0984e3 100%
                                );
                                padding: 20px;
                                border-radius: 12px;
                                border-left: 4px solid #0984e3;
                              "
                            >
                              <p
                                style="
                                  margin: 0;
                                  color: #ffffff;
                                  font-size: 16px;
                                  line-height: 1.6;
                                  font-weight: 500;
                                "
                              >
                                {{$json["message"]["content"]}}
                              </p>
                            </div>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>

                <!-- Issues Section -->
                <table width="100%" cellpadding="0" cellspacing="0">
                  <tr>
                    <td
                      style="
                        padding: 25px 40px;
                        border-bottom: 1px solid #f0f0f0;
                      "
                    >
                      <table width="100%" cellpadding="0" cellspacing="0">
                        <tr>
                          <td>
                            <h3
                              style="
                                margin: 0 0 15px;
                                font-size: 20px;
                                font-weight: 600;
                                color: #2c3e50;
                                display: flex;
                                align-items: center;
                              "
                            >
                              <span
                                style="
                                  display: inline-block;
                                  width: 4px;
                                  height: 20px;
                                  background: linear-gradient(
                                    to bottom,
                                    #e74c3c,
                                    #c0392b
                                  );
                                  margin-right: 12px;
                                  border-radius: 2px;
                                "
                              ></span>
                              🚨 Critical Issues
                            </h3>
                            <div
                              style="
                                background: linear-gradient(
                                  135deg,
                                  #fdcb6e 0%,
                                  #e17055 100%
                                );
                                padding: 18px;
                                border-radius: 12px;
                                border-left: 4px solid #e17055;
                              "
                            >
                              <p
                                style="
                                  margin: 0;
                                  color: #ffffff;
                                  font-size: 15px;
                                  line-height: 1.6;
                                  font-weight: 500;
                                "
                              >
                                <span
                                  class="status-indicator status-critical"
                                ></span>
                                {{
                                $items("Handle_API_Error")[0].json.issues.join(",
                                ") }}
                              </p>
                            </div>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>

                <!-- Services Section -->
                <table width="100%" cellpadding="0" cellspacing="0">
                  <tr>
                    <td style="padding: 25px 40px 35px">
                      <table width="100%" cellpadding="0" cellspacing="0">
                        <tr>
                          <td>
                            <h3
                              style="
                                margin: 0 0 20px;
                                font-size: 20px;
                                font-weight: 600;
                                color: #2c3e50;
                                display: flex;
                                align-items: center;
                              "
                            >
                              <span
                                style="
                                  display: inline-block;
                                  width: 4px;
                                  height: 20px;
                                  background: linear-gradient(
                                    to bottom,
                                    #00b894,
                                    #00a085
                                  );
                                  margin-right: 12px;
                                  border-radius: 2px;
                                "
                              ></span>
                              ⚙️ Affected Services
                            </h3>
                            <div
                              style="
                                background: linear-gradient(
                                  135deg,
                                  #a29bfe 0%,
                                  #6c5ce7 100%
                                );
                                padding: 20px;
                                border-radius: 12px;
                                border-left: 4px solid #6c5ce7;
                              "
                            >
                              <div
                                style="
                                  color: #ffffff;
                                  font-size: 15px;
                                  line-height: 1.8;
                                "
                              >
                                {{ Object.entries(
                                $items("Handle_API_Error")[0].json.services )
                                .map( ([key, val]) => `
                                <div
                                  style="
                                    margin: 8px 0;
                                    padding: 8px 12px;
                                    background: rgba(255, 255, 255, 0.15);
                                    border-radius: 8px;
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                  "
                                >
                                  <span style="font-weight: 500"
                                    >🔧 ${key}</span
                                  >
                                  <span
                                    style="
                                      padding: 4px 12px;
                                      background: rgba(255, 255, 255, 0.2);
                                      border-radius: 16px;
                                      font-size: 13px;
                                      font-weight: 600;
                                    "
                                    >${val}</span
                                  >
                                </div>
                                ` ) .join("") }}
                              </div>
                            </div>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>

            <!-- Action Section -->
            <tr>
              <td
                style="
                  background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
                  padding: 25px 40px;
                "
              >
                <table width="100%" cellpadding="0" cellspacing="0">
                  <tr>
                    <td align="center">
                      <h4
                        style="
                          margin: 0 0 15px;
                          color: #ffffff;
                          font-size: 18px;
                          font-weight: 600;
                        "
                      >
                        🔔 Next Steps Required
                      </h4>
                      <p
                        style="
                          margin: 0 0 20px;
                          color: rgba(255, 255, 255, 0.8);
                          font-size: 14px;
                          line-height: 1.5;
                        "
                      >
                        Please review the above issues and take immediate action
                        to resolve system problems.
                      </p>
                      <table cellpadding="0" cellspacing="0">
                        <tr>
                          <td
                            style="
                              background: linear-gradient(
                                45deg,
                                #00b894,
                                #00a085
                              );
                              padding: 12px 30px;
                              border-radius: 25px;
                              border: none;
                            "
                          >
                            <a
                              href="https://crop-pilot-api.azurewebsites.net/health"
                              style="
                                color: #ffffff;
                                text-decoration: none;
                                font-weight: 600;
                                font-size: 14px;
                                letter-spacing: 0.5px;
                              "
                            >
                              VIEW DASHBOARD →
                            </a>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>

            <!-- Footer -->
            <tr>
              <td
                style="
                  background: #f8f9fa;
                  padding: 20px 40px;
                  border-top: 1px solid #e9ecef;
                "
              >
                <table width="100%" cellpadding="0" cellspacing="0">
                  <tr>
                    <td align="center">
                      <p
                        style="
                          margin: 0 0 8px;
                          font-size: 13px;
                          color: #6c757d;
                          font-weight: 500;
                        "
                      >
                        🤖 n8n Workflow Monitoring System
                      </p>
                      <p style="margin: 0; font-size: 12px; color: #adb5bd">
                        Automated alert generated on {{ new
                        Date().toLocaleString() }} |
                        <a
                          href="#"
                          style="color: #007bff; text-decoration: none"
                          >Unsubscribe</a
                        >
                        |
                        <a
                          href="#"
                          style="color: #007bff; text-decoration: none"
                          >Contact Support</a
                        >
                      </p>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>

          <!-- Mobile Responsive Note -->
          <table
            width="650"
            cellpadding="0"
            cellspacing="0"
            style="margin-top: 20px"
          >
            <tr>
              <td align="center">
                <p
                  style="
                    margin: 0;
                    font-size: 11px;
                    color: #95a5a6;
                    font-style: italic;
                  "
                >
                  💡 This email is optimized for all devices and email clients
                </p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>
