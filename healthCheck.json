{"name": "healthCheck", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 3}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-2500, -40], "id": "7ad066ed-1a0f-4e80-885b-90cc020932b6", "name": "Schedule Trigger"}, {"parameters": {"url": "https://cropf-pilot-api.azurewebsites.net/health", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-2280, 60], "id": "f0a0dc8f-5712-4fbd-9a7a-fdfcfec7f3b7", "name": "HTTP Request", "onError": "continueRegularOutput"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4o-mini"}, "messages": {"values": [{"content": "=summarize the problem in this JSON, make it clear and easy to understand, and suggest a solution for it:\n{{ JSON.stringify($input.all()[1].json, null, 2) }}\n"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-1140, 60], "id": "4427eab4-3771-45b1-9e18-8c6e98dca113", "name": "Message a model", "credentials": {"openAiApi": {"id": "xsbntiGV2JXS0NZH", "name": "n8n free OpenAI API credits"}}}, {"parameters": {"sendTo": "<EMAIL>", "subject": "Critical , system have a problem", "message": "=🚨 SYSTEM ALERT!\n\nSummary:\n{{$json[\"message\"][\"content\"]}}\n\n-----------------------\n🧱 Issues:\n{{ $items(\"Handle API Error\")[0].json.issues.join(\", \") }}\n\n🔧 Services:\n{{ Object.entries($items(\"Handle API Error\")[0].json.services)\n     .map(([k,v]) => k + \": \" + v).join(\", \") }}\n", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-700, -20], "id": "31cf8614-fc66-4081-b0ba-27ea93e3c405", "name": "Send a message", "webhookId": "73bd8c66-4227-469c-a14a-271b6e77c1af", "credentials": {"gmailOAuth2": {"id": "QWKw5s7BDQMVi3FB", "name": "Gmail account"}}}, {"parameters": {"chatId": "*********", "text": "=🚨 SYSTEM ALERT!\n\nSummary:\n{{$json[\"message\"][\"content\"]}}\n\n-----------------------\n🧱 Issues:\n{{ $items(\"Handle API Error\")[0].json.issues.join(\", \") }}\n\n🔧 Services:\n{{ Object.entries($items(\"Handle API Error\")[0].json.services)\n     .map(([k,v]) => k + \": \" + v).join(\", \") }}\n", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [-720, 200], "name": "Send Telegram Alert", "id": "520b5779-6975-485d-b628-191e1f9ec5b2", "webhookId": "ba88fa57-a785-41b8-b7ab-88a095950b92", "credentials": {"telegramApi": {"id": "ABLc38odkR8y7l9G", "name": "Telegram account"}}}, {"parameters": {"rule": {"interval": [{"daysInterval": 7}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-2500, 660], "id": "b1764e52-df2d-4dd9-8b4d-d722ae88d65f", "name": "Schedule Trigger1"}, {"parameters": {"sendTo": "<EMAIL>", "subject": "Critical , system have a problem", "emailType": "text", "message": "k", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-1680, 660], "id": "8b4461c8-6e99-4054-9914-51a74a384e24", "name": "Send a message1", "webhookId": "73bd8c66-4227-469c-a14a-271b6e77c1af", "credentials": {"gmailOAuth2": {"id": "QWKw5s7BDQMVi3FB", "name": "Gmail account"}}}, {"parameters": {"chatId": "*********", "text": "k", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [-1460, 660], "name": "Send Telegram Alert1", "id": "7ca1ee22-6576-4927-86e6-4fc9f7da2087", "webhookId": "ba88fa57-a785-41b8-b7ab-88a095950b92", "credentials": {"telegramApi": {"id": "ABLc38odkR8y7l9G", "name": "Telegram account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4o-mini"}, "messages": {"values": [{"content": "=summarize the problem in this make it clear and easy to understand and suggest solution for it{{ $json.result }}services"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-2060, 660], "id": "a4fe2cee-85ab-4f9d-8d17-900becc756af", "name": "Message a model1", "credentials": {"openAiApi": {"id": "xsbntiGV2JXS0NZH", "name": "n8n free OpenAI API credits"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO SystemStatusHistory (status, issues, services, summary)\nVALUES (\n  '{{ $input.all()[1].json.status }}',\n  '{{ $input.all()[1].json.issues ? $input.all()[1].json.issues.join(\", \") : \"\" }}',\n  '{{ $input.all()[1].json.services ? Object.entries($input.all()[1].json.services).map(([k,v]) => k + \": \" + v).join(\", \") : \"\" }}',\n  '{{ $input.all()[1].json.summary }}'\n);\n"}, "type": "n8n-nodes-base.microsoftSql", "typeVersion": 1.1, "position": [-1040, -160], "id": "4b6c1938-eebb-47af-81d6-1a8837dd001a", "name": "SystemStatusHistory Update", "credentials": {"microsoftSql": {"id": "4NsG6UpyjQJ3O3TN", "name": "Microsoft SQL account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM SystemStatusHistory\nWHERE timestamp >= DATEADD(day, -7, GETDATE())\nORDER BY timestamp DESC;"}, "type": "n8n-nodes-base.microsoftSql", "typeVersion": 1.1, "position": [-2280, 660], "id": "9bbe9400-376b-4156-b34b-d574d6bef962", "name": "GetLastWeekHistory", "credentials": {"microsoftSql": {"id": "4NsG6UpyjQJ3O3TN", "name": "Microsoft SQL account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "e7393f57-9196-4d35-996f-735d750cca9b", "leftValue": "={{ $input.all()[1].json.status }}", "rightValue": "={{ $input.all()[0].json.status }}", "operator": {"type": "string", "operation": "notEquals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1620, -40], "id": "a78b53fa-055c-4b24-9c84-c92f88a5a3b2", "name": "ifCurrentStateNotLIkePreviousState"}, {"parameters": {"jsCode": "const item = $input.item;\nconst output = {\n  status: \"unknown\",\n  summary: \"No summary available\",\n  issues: [],\n  services: {},\n  timestamp: new Date().toISOString(),\n};\n\ntry {\n  if (item.json.status) {\n    return [{ json: item.json }];\n  }\n\n  const errorMsg = item.json?.error?.message;\n  if (typeof errorMsg === \"string\" && errorMsg.includes(\"{\")) {\n    // Extract JSON from messages like: '500 - \"{JSON_CONTENT}\"'\n    const match = errorMsg.match(/\"({[\\s\\S]*})\"/); \n    if (match && match[1]) {\n      // Unescape the JSON string\n      const jsonString = match[1].replace(/\\\\\"/g, '\"');\n      const parsed = JSON.parse(jsonString);\n      output.status = parsed.status || \"unhealthy\";\n      output.summary = parsed.summary || \"System issue detected\";\n      output.issues = parsed.issues || [];\n      output.services = parsed.services || {};\n      output.timestamp = parsed.timestamp || output.timestamp;\n      return [{ json: output }];\n    }\n    \n    // Fallback: try direct JSON match\n    const directMatch = errorMsg.match(/{[\\s\\S]*}/);\n    if (directMatch && directMatch[0]) {\n      const parsed = JSON.parse(directMatch[0]);\n      output.status = parsed.status || \"unhealthy\";\n      output.summary = parsed.summary || \"System issue detected\";\n      output.issues = parsed.issues || [];\n      output.services = parsed.services || {};\n      output.timestamp = parsed.timestamp || output.timestamp;\n      return [{ json: output }];\n    }\n  }\n\n  output.status = \"unhealthy\";\n  output.summary = errorMsg?.slice(0, 300) || \"Unrecognized server error.\";\n} catch (err) {\n  output.status = \"unhealthy\";\n  output.summary = `Parse error: ${err.message}`;\n}\n\nreturn [{ json: output }];"}, "name": "Handle API Error", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2060, 60], "id": "faf67359-f449-46d1-b880-9fa559895662"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT TOP 1 * FROM SystemCurrentStatus"}, "type": "n8n-nodes-base.microsoftSql", "typeVersion": 1.1, "position": [-2060, -140], "id": "0c82448d-6110-48eb-b533-6f9a964fe771", "name": "GetLastStatus", "alwaysOutputData": false, "credentials": {"microsoftSql": {"id": "4NsG6UpyjQJ3O3TN", "name": "Microsoft SQL account"}}}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [-1840, -40], "id": "e5dd22c0-7f76-452d-b1a3-8eb4dc0e68e9", "name": "<PERSON><PERSON>"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "57beb1fc-0f21-463c-82bf-3979f0c0cecb", "leftValue": "={{ $input.all()[1].json.status }}", "rightValue": "unhealthy", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1400, 60], "id": "b6d86a43-5176-4cab-b0af-a1749fc8ecc2", "name": "If"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE SystemCurrentStatus SET status = '{{ $input.all()[1].json.status }}' WHERE id = 1;"}, "type": "n8n-nodes-base.microsoftSql", "typeVersion": 1.1, "position": [-1400, -140], "id": "5abee59b-fd10-4ffd-96f8-70c148755c74", "name": "Update_CurrentSystemStatus_Unhealty", "credentials": {"microsoftSql": {"id": "4NsG6UpyjQJ3O3TN", "name": "Microsoft SQL account"}}}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}, {"node": "GetLastStatus", "type": "main", "index": 0}]]}, "Message a model": {"main": [[{"node": "Send a message", "type": "main", "index": 0}, {"node": "Send Telegram Alert", "type": "main", "index": 0}]]}, "Send a message": {"main": [[]]}, "Schedule Trigger1": {"main": [[{"node": "GetLastWeekHistory", "type": "main", "index": 0}]]}, "Send a message1": {"main": [[{"node": "Send Telegram Alert1", "type": "main", "index": 0}]]}, "Message a model1": {"main": [[{"node": "Send a message1", "type": "main", "index": 0}]]}, "Send Telegram Alert": {"main": [[]]}, "GetLastWeekHistory": {"main": [[{"node": "Message a model1", "type": "main", "index": 0}]]}, "ifCurrentStateNotLIkePreviousState": {"main": [[{"node": "If", "type": "main", "index": 0}, {"node": "Update_CurrentSystemStatus_Unhealty", "type": "main", "index": 0}], []]}, "HTTP Request": {"main": [[{"node": "Handle API Error", "type": "main", "index": 0}]]}, "GetLastStatus": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Handle API Error": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "ifCurrentStateNotLIkePreviousState", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "SystemStatusHistory Update", "type": "main", "index": 0}, {"node": "Message a model", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "ddffdd2d-81ed-449d-b737-6b4aaa33cef9", "meta": {"templateCredsSetupCompleted": true, "instanceId": "5a41d6b80f4619ebc9780d9a352cd73032290178972ecace3ee8f4b19099b358"}, "id": "3WQg0MTsnyMCPINO", "tags": []}